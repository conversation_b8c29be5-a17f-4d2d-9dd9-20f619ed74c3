# CarrotsBot - RLBot Integration

This directory contains the RLBot integration for your trained "Carrots" PPO bot. The bot uses the sophisticated reward system you developed and can play against humans or other bots in Rocket League.

## Files Overview

- `rlbot_carrots_bot.py` - Main bot implementation for RLBot
- `export_model.py` - Script to export your trained PPO model
- `setup_rlbot.py` - Automated setup script
- `carrots_bot.cfg` - Bot configuration for RLBot
- `appearance.cfg` - Bot appearance settings
- `carrots_match.cfg` - Match configuration for testing
- `carrots_bot_policy.pt` - Exported model (created after running setup)

## Quick Start

### 1. Install Requirements

```bash
pip install rlbot torch numpy
```

### 2. Export and Setup

Run the automated setup script:

```bash
python setup_rlbot.py
```

This will:
- Export your latest trained model from the checkpoints
- Verify all files are in place
- Set up the RLBot configuration

### 3. Run the Bot

Make sure Rocket League is running, then:

```bash
rlbot carrots_match.cfg
```

Or run with automatic launch:

```bash
python setup_rlbot.py --run
```

## Manual Setup (if needed)

### 1. Export Model

```bash
python export_model.py
```

This creates `carrots_bot_policy.pt` from your latest checkpoint.

### 2. Test the Bot

```bash
rlbot carrots_match.cfg
```

## Bot Features

Your CarrotsBot includes all the sophisticated behaviors from your training:

### Positive Behaviors (Carrots):
- **Ball Proximity** - Stays close to the ball
- **Ball Possession** - Controls and maintains ball possession
- **Field Control** - Keeps ball on opponent's side
- **Offensive Play** - Moves ball toward opponent goal
- **Speed & Aggression** - High-speed movement toward objectives
- **Landing Control** - Lands on wheels (sophisticated car control)
- **Flip Discipline** - Controlled, purposeful flipping
- **Boost Management** - Efficient boost usage and conservation
- **Aerial Control** - Controlled aerial movement and touches
- **Smooth Movement** - Smooth, controlled movement patterns
- **Car Orientation** - Proper orientation toward objectives

### Avoids Negative Behaviors:
- Ball chasing without purpose
- Wrong side positioning
- Idle/camping behavior
- Flip spamming
- Wasteful boost usage
- Erratic movement

## Customization

### Bot Appearance
Edit `appearance.cfg` to change the bot's car, colors, and accessories.

### Match Settings
Edit `carrots_match.cfg` to change:
- Number of bots
- Game mode
- Map
- Match length
- Mutators

### Bot Behavior
The bot uses the trained model, but you can adjust:
- Observation normalization in `RLBotObsBuilder`
- Action parsing in `RLBotActionParser`
- Model determinism (set `deterministic=False` for more varied play)

## Troubleshooting

### Model Not Found
- Make sure you have trained checkpoints in `data/checkpoints/`
- Run `python export_model.py` manually
- Check that `carrots_bot_policy.pt` exists

### RLBot Not Starting
- Make sure Rocket League is running
- Install RLBot: `pip install rlbot`
- Try running: `rlbot --help` to verify installation

### Bot Not Moving
- Check console for error messages
- Verify model loaded successfully
- Try reducing observation size if there are shape mismatches

### Performance Issues
- The bot runs at 120 FPS by default
- Reduce `maximum_tick_rate_preference` in `carrots_bot.cfg` if needed
- Make sure PyTorch is using CPU (not GPU) for inference

## Model Information

Your exported model:
- Uses the latest checkpoint from training
- Includes the full "Carrots" reward system behaviors
- Trained for sophisticated Rocket League gameplay
- Supports deterministic and stochastic action selection

## Playing Against the Bot

1. Start a match with `rlbot carrots_match.cfg`
2. The default configuration spawns 2 CarrotsBots (one per team)
3. You can modify the config to add human players
4. The bot will demonstrate the behaviors learned from your reward system

Enjoy playing against your trained CarrotsBot! 🥕🚗⚽
