import numpy as np
from rlgym_sim.utils.gamestates import GameState
from rlgym_ppo.util import <PERSON>rics<PERSON>ogger
from rocketsimvis_rlgym_sim_client import send_state_to_rocketsimvis
from custom_obs import AdvancedObsBuilder


class CarrotsMetricsLogger(MetricsLogger):
    """Enhanced metrics logger for tracking CarrotsReward performance and game statistics."""

    def _collect_metrics(self, game_state: GameState) -> list:
        metrics = []

        # Basic movement metrics
        player = game_state.players[0]
        metrics.append(player.car_data.linear_velocity)
        metrics.append(np.linalg.norm(player.car_data.linear_velocity))  # Speed

        # Ball interaction metrics
        ball_distance = np.linalg.norm(game_state.ball.position - player.car_data.position)
        metrics.append(ball_distance)

        # Field position metrics
        metrics.append(player.car_data.position)
        metrics.append(game_state.ball.position)

        # Game state metrics
        metrics.append(game_state.blue_score)
        metrics.append(game_state.orange_score)

        # Player state metrics
        metrics.append(player.boost_amount)
        metrics.append(1.0 if player.on_ground else 0.0)
        metrics.append(1.0 if player.has_flip else 0.0)

        return metrics

    def _report_metrics(self, collected_metrics, wandb_run, cumulative_timesteps):
        if not collected_metrics:
            return

        # Initialize accumulators
        avg_velocity = np.zeros(3)
        avg_speed = 0.0
        avg_ball_distance = 0.0
        avg_player_pos = np.zeros(3)
        avg_ball_pos = np.zeros(3)
        avg_boost = 0.0
        ground_time_ratio = 0.0
        flip_availability_ratio = 0.0

        # Aggregate metrics
        for metrics in collected_metrics:
            avg_velocity += metrics[0]
            avg_speed += metrics[1]
            avg_ball_distance += metrics[2]
            avg_player_pos += metrics[3]
            avg_ball_pos += metrics[4]
            avg_boost += metrics[7]
            ground_time_ratio += metrics[8]
            flip_availability_ratio += metrics[9]

        # Calculate averages
        n_samples = len(collected_metrics)
        avg_velocity /= n_samples
        avg_speed /= n_samples
        avg_ball_distance /= n_samples
        avg_player_pos /= n_samples
        avg_ball_pos /= n_samples
        avg_boost /= n_samples
        ground_time_ratio /= n_samples
        flip_availability_ratio /= n_samples

        # --- Score reporting fix ---
        blue_goals_in_batch = collected_metrics[-1][5] - collected_metrics[0][5]
        orange_goals_in_batch = collected_metrics[-1][6] - collected_metrics[0][6]

        # Create comprehensive report
        report = {
            # Movement metrics
            "avg_speed": avg_speed,
            "avg_velocity_x": avg_velocity[0],
            "avg_velocity_y": avg_velocity[1],
            "avg_velocity_z": avg_velocity[2],

            # Ball interaction
            "avg_ball_distance": avg_ball_distance,
            "ball_proximity_score": max(0, 1000 - avg_ball_distance) / 1000,  # Normalized proximity

            # Field positioning
            "avg_player_x": avg_player_pos[0],
            "avg_player_y": avg_player_pos[1],
            "avg_player_z": avg_player_pos[2],
            "avg_ball_x": avg_ball_pos[0],
            "avg_ball_y": avg_ball_pos[1],
            "avg_ball_z": avg_ball_pos[2],

            # Game performance (batch goal counts)
            "blue_goals_in_batch": blue_goals_in_batch,
            "orange_goals_in_batch": orange_goals_in_batch,
            "score_differential": blue_goals_in_batch - orange_goals_in_batch,

            # Player state
            "avg_boost_amount": avg_boost * 100,  # Convert to percentage
            "ground_time_ratio": ground_time_ratio * 100,
            "flip_availability_ratio": flip_availability_ratio * 100,

            # Training progress
            "cumulative_timesteps": cumulative_timesteps
        }

        wandb_run.log(report)


def build_dribble_env():
    import rlgym_sim
    import numpy as np
    from rlgym_sim.utils.reward_functions import CombinedReward
    from rlgym_sim.utils.obs_builders import DefaultObs
    from rlgym_sim.utils.terminal_conditions.common_conditions import GoalScoredCondition
    from rlgym_sim.utils.action_parsers import ContinuousAction
    from rlgym_sim.utils import common_values
    from custom_rewards import ApproachBallReward, ShootAtNetReward, GotDemoedPenalty, WallTouchReward, BallAirReward, PowerShotReward
    from rlgym_sim.utils.reward_functions.common_rewards import EventReward

    # --- Environment settings ---
    spawn_opponents = True
    team_size = 2
    tick_skip = 1200  # 1 second real time = 60 seconds in-game at 60 FPS

    # --- Action parser: use default continuous actions ---
    action_parser = ContinuousAction()

    # --- Done/terminal conditions ---
    terminal_conditions = [
        GoalScoredCondition(),
    ]

    # --- Reward functions ---
    rewards_to_combine = [
        EventReward(goal=10, concede=-10, shot=1, save=2, demo=3),  # Event-based rewards (demos included)
        ApproachBallReward(),                                       # Shaping reward
        ShootAtNetReward(),                                        # Sophisticated shooting reward
        GotDemoedPenalty(),                                        # Negative reward for getting demoed
        WallTouchReward(),                                         # Reward for wall play
        BallAirReward(),                                           # Reward for getting ball in the air
        PowerShotReward(),                                         # Reward for power shots
    ]
    reward_weights = [
        1.0,   # EventReward
        0.1,   # ApproachBallReward
        0.5,   # ShootAtNetReward
        1.0,   # GotDemoedPenalty
        0.2,   # WallTouchReward
        0.2,   # BallAirReward
        0.3,   # PowerShotReward
    ]
    reward_fn = CombinedReward(reward_functions=rewards_to_combine, reward_weights=reward_weights)

    # --- Observation builder: advanced custom ---
    obs_builder = AdvancedObsBuilder()

    env = rlgym_sim.make(
        tick_skip=tick_skip,
        team_size=team_size,
        spawn_opponents=spawn_opponents,
        terminal_conditions=terminal_conditions,
        reward_fn=reward_fn,
        obs_builder=obs_builder,
        action_parser=action_parser
    )

    # Visualization (optional)
    import rocketsimvis_rlgym_sim_client as rsv
    type(env).render = lambda self: rsv.send_state_to_rocketsimvis(self._prev_state)

    return env


if __name__ == "__main__":
    from rlgym_ppo import Learner
    metrics_logger = CarrotsMetricsLogger()
    n_proc = 10
    min_inference_size = max(1, int(round(n_proc * 0.9)))
    learner = Learner(
        build_dribble_env,
        n_proc=n_proc,
        min_inference_size=min_inference_size,
        metrics_logger=metrics_logger,
        ts_per_iteration=50000,
        exp_buffer_size=100000,
        ppo_batch_size=50000,
        ppo_minibatch_size=25000,
        ppo_ent_coef=0.01,
        ppo_epochs=2,
        policy_lr=1e-4,
        critic_lr=1e-4,
        policy_layer_sizes=[2048, 2048, 1024, 1024],
        critic_layer_sizes=[2048, 2048, 1024, 1024],
        standardize_returns=True,
        standardize_obs=False,
        save_every_ts=2_000_000,
        timestep_limit=1_000_000_000,
        log_to_wandb=True,
        render=True
    )
    learner.learn()