from collections import Counter
from typing import Any

import numpy as np
from rlgym_sim.utils.obs_builders import ObsBuilder
from rlgym_sim.utils.gamestates import GameState, PlayerData

BOOST_LOCATIONS = (
    (0.0, -4240.0, 70.0),
    (-1792.0, -4184.0, 70.0),
    (1792.0, -4184.0, 70.0),
    (-3072.0, -4096.0, 73.0),
    (3072.0, -4096.0, 73.0),
    (- 940.0, -3308.0, 70.0),
    (940.0, -3308.0, 70.0),
    (0.0, -2816.0, 70.0),
    (-3584.0, -2484.0, 70.0),
    (3584.0, -2484.0, 70.0),
    (-1788.0, -2300.0, 70.0),
    (1788.0, -2300.0, 70.0),
    (-2048.0, -1036.0, 70.0),
    (0.0, -1024.0, 70.0),
    (2048.0, -1036.0, 70.0),
    (-3584.0, 0.0, 73.0),
    (-1024.0, 0.0, 70.0),
    (1024.0, 0.0, 70.0),
    (3584.0, 0.0, 73.0),
    (-2048.0, 1036.0, 70.0),
    (0.0, 1024.0, 70.0),
    (2048.0, 1036.0, 70.0),
    (-1788.0, 2300.0, 70.0),
    (1788.0, 2300.0, 70.0),
    (-3584.0, 2484.0, 70.0),
    (3584.0, 2484.0, 70.0),
    (0.0, 2816.0, 70.0),
    (- 940.0, 3310.0, 70.0),
    (940.0, 3308.0, 70.0),
    (-3072.0, 4096.0, 73.0),
    (3072.0, 4096.0, 73.0),
    (-1792.0, 4184.0, 70.0),
    (1792.0, 4184.0, 70.0),
    (0.0, 4240.0, 70.0),
)


class NectoObsBuilder:
    _invert = np.array([1] * 5 + [-1, -1, 1] * 5 + [1] * 4)
    _norm = np.array([1.] * 5 + [2300] * 6 + [1] * 6 + [5.5] * 3 + [1] * 4)

    def __init__(self, tick_skip=8, field_info=None):
        super().__init__()
        self.demo_timers = None
        self.boost_timers = None
        self.current_state = None
        self.current_qkv = None
        self.current_mask = None
        self.tick_skip = tick_skip
        if field_info is None:
            self._boost_locations = np.array(BOOST_LOCATIONS)
            self._boost_types = self._boost_locations[:, 2] > 72
        else:
            self._boost_locations = np.array([[bp.location.x, bp.location.y, bp.location.z]
                                              for bp in field_info.boost_pads[:field_info.num_boosts]])
            self._boost_types = np.array([bp.is_full_boost for bp in field_info.boost_pads[:field_info.num_boosts]])

    def reset(self, initial_state: GameState):
        self.demo_timers = Counter()
        self.boost_timers = np.zeros(len(initial_state.boost_pads))
        # self.current_state = initial_state

    def _maybe_update_obs(self, state: GameState):
        # Don't need to do this for RLBot
        # if state == self.current_state:  # No need to update
        #     return

        if self.boost_timers is None:
            self.reset(state)
        else:
            self.current_state = state

        qkv = np.zeros((1, 1 + len(state.players) + len(state.boost_pads), 24))  # Ball, players, boosts

        # Add ball
        n = 0
        ball = state.ball
        qkv[0, 0, 3] = 1  # is_ball
        qkv[0, 0, 5:8] = ball.position
        qkv[0, 0, 8:11] = ball.linear_velocity
        qkv[0, 0, 17:20] = ball.angular_velocity

        # Add players
        n += 1
        for player in state.players:
            if player.team_num == 0:
                qkv[0, n, 1] = 1  # is_teammate
            else:
                qkv[0, n, 2] = 1  # is_opponent
            car_data = player.car_data
            qkv[0, n, 5:8] = car_data.position
            qkv[0, n, 8:11] = car_data.linear_velocity
            qkv[0, n, 11:14] = car_data.forward()
            qkv[0, n, 14:17] = car_data.up()
            qkv[0, n, 17:20] = car_data.angular_velocity
            qkv[0, n, 20] = player.boost_amount
            #             qkv[0, n, 21] = player.is_demoed
            qkv[0, n, 22] = player.on_ground
            qkv[0, n, 23] = player.has_flip

            # Different than training to account for varying player amounts
            if self.demo_timers[player.car_id] <= 0:
                self.demo_timers[player.car_id] = 3
            else:
                self.demo_timers[player.car_id] = max(self.demo_timers[player.car_id] - self.tick_skip / 120, 0)
            qkv[0, n, 21] = self.demo_timers[player.car_id] / 10
            n += 1

        # Add boost pads
        n = 1 + len(state.players)
        boost_pads = state.boost_pads
        qkv[0, n:, 4] = 1  # is_boost
        qkv[0, n:, 5:8] = self._boost_locations
        qkv[0, n:, 20] = 0.12 + 0.88 * self._boost_types  # Boost amount
        #         qkv[0, n:, 21] = boost_pads

        # Boost and demo timers
        new_boost_grabs = (boost_pads == 1) & (self.boost_timers == 0)  # New boost grabs since last frame
        self.boost_timers[new_boost_grabs] = 0.4 + 0.6 * (self._boost_locations[new_boost_grabs, 2] > 72)
        self.boost_timers *= boost_pads  # Make sure we have zeros right
        qkv[0, 1 + len(state.players):, 21] = self.boost_timers
        self.boost_timers -= self.tick_skip / 1200  # Pre-normalized, 120 fps for 10 seconds
        self.boost_timers[self.boost_timers < 0] = 0

        # Store results
        self.current_qkv = qkv / self._norm
        mask = np.zeros((1, qkv.shape[1]))
        mask[0, 1 + len(state.players):1 + len(state.players)] = 1
        self.current_mask = mask

    def build_obs(self, player: PlayerData, state: GameState, previous_action: np.ndarray) -> Any:
        self._maybe_update_obs(state)
        invert = player.team_num == 1

        qkv = self.current_qkv.copy()
        mask = self.current_mask.copy()

        main_n = state.players.index(player) + 1
        qkv[0, main_n, 0] = 1  # is_main
        if invert:
            qkv[0, :, (1, 2)] = qkv[0, :, (2, 1)]  # Swap blue/orange
            qkv *= self._invert  # Negate x and y values

        q = qkv[0, main_n, :]
        q = np.expand_dims(np.concatenate((q, previous_action), axis=0), axis=(0, 1))
        kv = qkv

        # Use relative coordinates
        kv[0, :, 5:11] -= q[0, 0, 5:11]
        return q, kv, mask


class AdvancedObsBuilder(ObsBuilder):
    """
    Observation includes: all cars' positions, velocities, rotations, angular velocities, team, on_ground, has_flip, boost;
    ball position, velocity, angular velocity, height, and predicted landing position/time.
    """

    def build_obs(self, player: PlayerData, state: GameState, previous_action: np.ndarray):
        # Ball features
        ball = state.ball
        ball_pos = ball.position / np.array([4096, 5120, 2044])
        ball_vel = ball.linear_velocity / 6000
        ball_ang_vel = ball.angular_velocity / 5.5
        ball_height = np.array([ball.position[2] / 2044])

        # Ball landing prediction (simple physics: where will it hit z=92)
        g = -650  # gravity in uu/s^2
        z0 = ball.position[2]
        vz = ball.linear_velocity[2]
        t_land = 0
        if vz < 0:
            t_land = (92 - z0) / vz  # time to reach z=92
        else:
            # If going up, time to apex + time to fall
            t_apex = -vz / g
            z_apex = z0 + vz * t_apex + 0.5 * g * t_apex ** 2
            t_fall = np.sqrt(2 * max(0, z_apex - 92) / -g)
            t_land = t_apex + t_fall
        t_land = np.clip(t_land, 0, 6)  # max 6s
        pred_xy = ball.position[:2] + ball.linear_velocity[:2] * t_land
        pred_xy = pred_xy / np.array([4096, 5120])
        ball_landing = np.concatenate([pred_xy, [t_land / 6]])

        # Car features (for all cars)
        car_obs = []
        for p in state.players:
            car = p.car_data
            car_pos = car.position / np.array([4096, 5120, 2044])
            car_vel = car.linear_velocity / 2300
            car_ang_vel = car.angular_velocity / 5.5
            # Defensive: ensure car_rot is always a 1D array of length 3
            car_rot = getattr(car, 'euler_angles', None)
            if car_rot is None or np.shape(car_rot) != (3,):
                car_rot = np.zeros(3)
            team = np.array([1.0 if p.team_num == player.team_num else -1.0])
            on_ground = np.array([1.0 if p.on_ground else 0.0])
            has_flip = np.array([1.0 if p.has_flip else 0.0])
            boost = np.array([p.boost_amount])
            car_obs.append(np.concatenate([
                car_pos, car_vel, car_ang_vel, car_rot, team, on_ground, has_flip, boost
            ]))
        car_obs = np.concatenate(car_obs)

        obs = np.concatenate([
            ball_pos, ball_vel, ball_ang_vel, ball_height, ball_landing, car_obs
        ])
        return obs

    def reset(self, initial_state: GameState):
        pass
