import numpy as np
from rlgym_sim.utils import RewardFunction
from rlgym_sim.utils.gamestates import GameState, PlayerData
from rlgym_sim.utils.common_values import CAR_MAX_SPEED, SIDE_WALL_X, BACK_NET_Y

class InAirReward(RewardFunction):
    def __init__(self):
        super().__init__()
    def reset(self, initial_state: GameState):
        pass
    def get_reward(self, player: PlayerData, state: GameState, previous_action):
        return 1.0 if not player.on_ground else 0.0

class SpeedTowardBallReward(RewardFunction):
    def __init__(self):
        super().__init__()
    def reset(self, initial_state: GameState):
        pass
    def get_reward(self, player: PlayerData, state: GameState, previous_action):
        player_vel = player.car_data.linear_velocity
        pos_diff = state.ball.position - player.car_data.position
        dist_to_ball = np.linalg.norm(pos_diff)
        if dist_to_ball == 0:
            return 0.0
        dir_to_ball = pos_diff / dist_to_ball
        speed_toward_ball = np.dot(player_vel, dir_to_ball)
        return max(0.0, speed_toward_ball / CAR_MAX_SPEED)


class CarrotsReward(RewardFunction):
    """
    Simplified for 1v1: removes rotation penalty and redundant sub-rewards.
    """
    def __init__(self):
        super().__init__()
        self.last_ball_touch_player = None
        self.possession_time = {}
        self.last_ball_position = None
        self.last_player_on_ground = {}
        self.flip_cooldown = {}
        self.last_flip_time = {}
        self.boost_usage = {}
        self.last_boost_amount = {}
        self.aerial_time = {}
        self.last_velocity = {}

    def reset(self, initial_state: GameState):
        self.last_ball_touch_player = None
        self.possession_time = {}
        self.last_ball_position = initial_state.ball.position.copy()
        self.last_player_on_ground = {}
        self.flip_cooldown = {}
        self.last_flip_time = {}
        self.boost_usage = {}
        self.last_boost_amount = {}
        self.aerial_time = {}
        self.last_velocity = {}

    def get_reward(self, player: PlayerData, state: GameState, previous_action):
        reward = 0.0

        # Initialize possession tracking for this player
        if player.car_id not in self.possession_time:
            self.possession_time[player.car_id] = 0

        # === BALL PROXIMITY CARROTS (0-15 points) ===
        ball_pos = state.ball.position
        player_pos = player.car_data.position
        dist_to_ball = np.linalg.norm(ball_pos - player_pos)

        # Reward being close to ball (exponential decay)
        max_reward_distance = 500  # Units where you get max reward
        proximity_reward = 15 * np.exp(-dist_to_ball / max_reward_distance)
        reward += proximity_reward

        # === BALL POSSESSION CARROTS (0-25 points) ===
        # Check if player is touching/near ball (possession)
        is_possessing = dist_to_ball < 200  # Close enough to be "possessing"

        if is_possessing:
            self.possession_time[player.car_id] += 1
            self.last_ball_touch_player = player.car_id
            # Reward active possession
            reward += 10

            # Bonus for sustained possession (up to 15 more points)
            possession_bonus = min(15, self.possession_time[player.car_id] * 0.5)
            reward += possession_bonus
        else:
            # Decay possession time when not touching ball
            self.possession_time[player.car_id] = max(0, self.possession_time[player.car_id] - 0.5)

        # === FIELD CONTROL CARROTS (0-20 points) ===
        # Reward keeping ball on opponent's side
        field_center_y = 0
        opponent_goal_y = BACK_NET_Y if player.team_num == 0 else -BACK_NET_Y

        # Ball position relative to field center
        ball_field_position = (ball_pos[1] - field_center_y) / BACK_NET_Y

        if player.team_num == 0:  # Blue team
            if ball_pos[1] > field_center_y:  # Ball on orange side
                field_control_reward = 20 * (ball_field_position)
                reward += max(0, field_control_reward)
        else:  # Orange team
            if ball_pos[1] < field_center_y:  # Ball on blue side
                field_control_reward = 20 * (-ball_field_position)
                reward += max(0, field_control_reward)

        # === OFFENSIVE PLAY CARROTS (0-25 points) ===
        # Reward moving ball toward opponent goal
        if self.last_ball_position is not None:
            ball_movement = ball_pos - self.last_ball_position

            # Direction to opponent goal
            if player.team_num == 0:  # Blue team attacking orange goal
                goal_direction = np.array([0, 1, 0])  # Positive Y
            else:  # Orange team attacking blue goal
                goal_direction = np.array([0, -1, 0])  # Negative Y

            # Reward if ball moved toward opponent goal
            ball_toward_goal = np.dot(ball_movement, goal_direction)
            if ball_toward_goal > 0 and is_possessing:
                offensive_reward = min(25, ball_toward_goal * 5)
                reward += offensive_reward

        # === SPEED AND AGGRESSION CARROTS (0-10 points) ===
        # Reward high speed when going toward ball or goal
        player_speed = np.linalg.norm(player.car_data.linear_velocity)
        speed_ratio = player_speed / CAR_MAX_SPEED

        # Direction to ball
        if dist_to_ball > 0:
            dir_to_ball = (ball_pos - player_pos) / dist_to_ball
            vel_toward_ball = np.dot(player.car_data.linear_velocity, dir_to_ball)

            if vel_toward_ball > 0:  # Moving toward ball
                speed_reward = 10 * speed_ratio * (vel_toward_ball / player_speed)
                reward += speed_reward

        # === POSSESSION LOSS PENALTY (-15 points) ===
        # Penalty if opponent gains possession after you had it
        if (self.last_ball_touch_player == player.car_id and
            not is_possessing and dist_to_ball > 400):
            # Check if any opponent is now closer to ball
            for other_player in state.players:
                if other_player.team_num != player.team_num:
                    other_dist = np.linalg.norm(ball_pos - other_player.car_data.position)
                    if other_dist < dist_to_ball and other_dist < 200:
                        reward -= 15  # Possession loss penalty
                        break

        # === DEFENSIVE POSITIONING PENALTY (-10 points) ===
        # Penalty for being too far from action when ball is on your side
        own_goal_y = -BACK_NET_Y if player.team_num == 0 else BACK_NET_Y

        # If ball is on your defensive side and you're far away
        if ((player.team_num == 0 and ball_pos[1] < field_center_y) or
            (player.team_num == 1 and ball_pos[1] > field_center_y)):
            if dist_to_ball > 1000:  # Too far from defensive action
                reward -= 10

        # === SOPHISTICATED MOVEMENT CARROTS ===

        # Initialize tracking for this player
        if player.car_id not in self.last_player_on_ground:
            self.last_player_on_ground[player.car_id] = player.on_ground
            self.flip_cooldown[player.car_id] = 0
            self.last_flip_time[player.car_id] = 0
            self.boost_usage[player.car_id] = 0
            self.last_boost_amount[player.car_id] = player.boost_amount
            self.aerial_time[player.car_id] = 0
            self.last_velocity[player.car_id] = player.car_data.linear_velocity.copy()

        # === LANDING CONTROL CARROTS (0-15 points) ===
        # Reward landing on wheels (good car control)
        was_airborne = not self.last_player_on_ground[player.car_id]
        is_grounded = player.on_ground

        if was_airborne and is_grounded:
            # Check if landed wheels-down (good orientation)
            car_up_vector = player.car_data.up()
            world_up = np.array([0, 0, 1])
            alignment = np.dot(car_up_vector, world_up)

            if alignment > 0.8:  # Landed mostly upright
                reward += 15
            elif alignment > 0.5:  # Decent landing
                reward += 8
            else:  # Poor landing (upside down)
                reward -= 5

        # === FLIP DISCIPLINE CARROTS (0-10 points / -10 penalty) ===
        # Detect flip usage (sudden change in angular velocity while airborne)
        if not player.on_ground:
            ang_vel_magnitude = np.linalg.norm(player.car_data.angular_velocity)
            if ang_vel_magnitude > 4.0:  # Likely flipping
                self.flip_cooldown[player.car_id] = 30  # 30 frame cooldown
                self.last_flip_time[player.car_id] = 0

        # Reward controlled flipping (not spamming)
        if self.flip_cooldown[player.car_id] > 0:
            self.flip_cooldown[player.car_id] -= 1
            self.last_flip_time[player.car_id] += 1

            # Penalty for flip spamming
            if self.last_flip_time[player.car_id] < 15:  # Too soon after last flip
                reward -= 10
            else:
                # Reward for controlled, purposeful flips
                if dist_to_ball < 300:  # Flipping near ball (good)
                    reward += 10

        # === BOOST MANAGEMENT CARROTS (0-8 points / -5 penalty) ===
        current_boost = player.boost_amount
        last_boost = self.last_boost_amount[player.car_id]
        boost_used = last_boost - current_boost

        if boost_used > 0:
            self.boost_usage[player.car_id] += boost_used

            # Reward efficient boost usage
            if player_speed > CAR_MAX_SPEED * 0.8:  # High speed
                reward += min(5, boost_used * 10)  # Reward boost for speed
            elif dist_to_ball < 500 and is_possessing:  # Boost while possessing
                reward += min(3, boost_used * 8)
            else:
                # Penalty for wasteful boost usage
                reward -= min(5, boost_used * 5)

        # Reward boost conservation when full
        if current_boost > 80:
            reward += 2

        # === AERIAL CONTROL CARROTS (0-12 points) ===
        if not player.on_ground:
            self.aerial_time[player.car_id] += 1

            # Reward controlled aerial movement
            if ball_pos[2] > 200:  # Ball is in air
                height_diff = abs(player_pos[2] - ball_pos[2])
                if height_diff < 100:  # Good aerial positioning
                    reward += 8

                # Bonus for aerial ball touches
                if dist_to_ball < 150:
                    reward += 4

        else:
            self.aerial_time[player.car_id] = 0

        # === SMOOTH MOVEMENT CARROTS (0-6 points / -8 penalty) ===
        # Reward smooth, controlled movement (less jittery)
        current_vel = player.car_data.linear_velocity
        last_vel = self.last_velocity[player.car_id]

        velocity_change = np.linalg.norm(current_vel - last_vel)

        # Penalty for erratic movement (too much jitter)
        if velocity_change > CAR_MAX_SPEED * 0.3:  # Sudden direction changes
            reward -= 8
        elif velocity_change < CAR_MAX_SPEED * 0.1:  # Smooth movement
            reward += 6

        # === CAR ORIENTATION CARROTS (0-5 points) ===
        # Reward keeping car oriented toward action
        if dist_to_ball > 0:
            car_forward = player.car_data.forward()
            dir_to_ball = (ball_pos - player_pos) / dist_to_ball
            orientation_alignment = np.dot(car_forward, dir_to_ball)

            if orientation_alignment > 0.7:  # Well oriented toward ball
                reward += 5
            elif orientation_alignment < -0.5:  # Facing away from ball
                reward -= 3

        # === NEGATIVE REWARD PENALTIES ===

        # === BALL CHASING PENALTY (-20 points) ===
        # Penalty for mindlessly chasing ball without purpose
        if dist_to_ball > 800 and player_speed > CAR_MAX_SPEED * 0.7:
            # Moving fast but far from ball = ball chasing
            reward -= 15

        # === WRONG SIDE PENALTY (-25 points) ===
        # Heavy penalty for being on wrong side of field
        if player.team_num == 0:  # Blue team
            if player_pos[1] < -BACK_NET_Y * 0.7:  # Too far in own goal area
                reward -= 25
        else:  # Orange team
            if player_pos[1] > BACK_NET_Y * 0.7:  # Too far in own goal area
                reward -= 25

        # === IDLE/CAMPING PENALTY (-15 points) ===
        # Penalty for not moving (camping/idle)
        if player_speed < CAR_MAX_SPEED * 0.1:  # Very slow
            reward -= 10

        # Extra penalty if idle while ball is moving fast
        ball_speed = np.linalg.norm(state.ball.linear_velocity) if hasattr(state.ball, 'linear_velocity') else 0
        if ball_speed > 1000 and player_speed < CAR_MAX_SPEED * 0.2:
            reward -= 15  # Ball is active but you're not

        # === OWN GOAL DANGER PENALTY (-30 points) ===
        # Severe penalty for hitting ball toward own goal
        if self.last_ball_position is not None and is_possessing:
            ball_movement = ball_pos - self.last_ball_position

            # Direction to own goal
            if player.team_num == 0:  # Blue team
                own_goal_direction = np.array([0, -1, 0])  # Negative Y
            else:  # Orange team
                own_goal_direction = np.array([0, 1, 0])   # Positive Y

            # Check if ball moved toward own goal
            ball_toward_own_goal = np.dot(ball_movement, own_goal_direction)
            if ball_toward_own_goal > 0:
                reward -= 30  # Severe penalty for own goal danger

        # === BOOST STARVATION PENALTY (-8 points) ===
        # Penalty for having no boost when you need it
        if current_boost < 10:
            if dist_to_ball > 500:  # Far from ball with no boost
                reward -= 8
            if not player.on_ground:  # In air with no boost
                reward -= 5

        # === POOR POSITIONING PENALTY (-12 points) ===
        # Penalty for being in bad positions relative to teammates
        # (This is simplified since we don't have full team context)

        # If ball is high and you're not positioned for aerial
        if ball_pos[2] > 300 and player_pos[2] < 50 and dist_to_ball < 400:
            if player.on_ground:  # Should be jumping for aerial
                reward -= 12

        # === WALL/CEILING CAMPING PENALTY (-10 points) ===
        # Penalty for staying on walls/ceiling too long without purpose
        if (abs(player_pos[0]) > SIDE_WALL_X * 0.8 or  # On side walls
            player_pos[2] > 300):  # High up (wall/ceiling)
            if dist_to_ball > 600:  # Far from action
                reward -= 10

        # === DEMO VULNERABILITY PENALTY (-5 points) ===
        # Penalty for being in vulnerable positions
        if player_speed < CAR_MAX_SPEED * 0.3:  # Moving slowly
            # Check if any opponent is nearby and fast (demo threat)
            for opponent in state.players:
                if opponent.team_num != player.team_num:
                    opponent_dist = np.linalg.norm(opponent.car_data.position - player_pos)
                    opponent_speed = np.linalg.norm(opponent.car_data.linear_velocity)

                    if opponent_dist < 300 and opponent_speed > CAR_MAX_SPEED * 0.8:
                        reward -= 5  # Vulnerable to demo
                        break

        # === SUPERSONIC WASTE PENALTY (-8 points) ===
        # Penalty for going supersonic without purpose
        if player_speed > CAR_MAX_SPEED * 0.95:  # Supersonic
            if dist_to_ball > 1000:  # Far from action
                reward -= 8
            if abs(player_pos[1]) > BACK_NET_Y * 0.8:  # In corner
                reward -= 5

        # === FLIP RESET WASTE PENALTY (-12 points) ===
        # Penalty for wasting flip resets
        if not player.on_ground and player.has_flip:
            # If you have flip but not using it purposefully
            if (dist_to_ball > 400 and
                self.aerial_time[player.car_id] > 60):  # Long aerial without purpose
                reward -= 12

        # Update tracking variables
        self.last_player_on_ground[player.car_id] = player.on_ground
        self.last_boost_amount[player.car_id] = current_boost
        self.last_velocity[player.car_id] = current_vel.copy()
        self.last_ball_position = ball_pos.copy()

        # Clamp reward to -50 to 120 range (allow negative rewards)
        return max(-50, min(120, reward))


class FaceBallReward(RewardFunction):
    def __init__(self):
        super().__init__()
    def reset(self, initial_state: GameState):
        pass
    def get_reward(self, player: PlayerData, state: GameState, previous_action):
        car_forward = player.car_data.forward()
        to_ball = state.ball.position - player.car_data.position
        to_ball_norm = to_ball / (np.linalg.norm(to_ball) + 1e-6)
        return np.dot(car_forward, to_ball_norm)

class CloseToBallReward(RewardFunction):
    def __init__(self):
        super().__init__()
    def reset(self, initial_state: GameState):
        pass
    def get_reward(self, player: PlayerData, state: GameState, previous_action):
        dist = np.linalg.norm(state.ball.position - player.car_data.position)
        return 1.0 - np.tanh(dist / 3000)

class NoUnnecessaryJumpReward(RewardFunction):
    def __init__(self):
        super().__init__()
    def reset(self, initial_state: GameState):
        pass
    def get_reward(self, player: PlayerData, state: GameState, previous_action):
        dist = np.linalg.norm(state.ball.position - player.car_data.position)
        jumped = previous_action[5] > 0.5  # Jump button index is 5 in most parsers
        # Only penalize jumping when the ball is low and player is on ground
        if jumped and dist > 1200 and state.ball.position[2] < 150 and player.on_ground:
            return -0.2
        return 0.0
