{"cumulative_timesteps": 13000124, "cumulative_model_updates": 100, "policy_average_reward": -242.66830980550193, "epoch": 25, "ts_since_last_save": 1000024, "reward_running_stats": {"mean": [-25.25391960144043], "var": [3543045.0], "shape": [1], "count": 3900}, "wandb_run_id": "yd7kf81k", "wandb_project": "rlgym-ppo", "wandb_entity": "isa1ah56dev-pinterest", "wandb_group": "unnamed-runs", "wandb_config": {"n_proc": 6, "min_inference_size": 5, "timestep_limit": 1000000000, "exp_buffer_size": 1000000, "ts_per_iteration": 500000, "standardize_returns": true, "standardize_obs": false, "policy_layer_sizes": [2048, 2048, 1024, 1024], "critic_layer_sizes": [2048, 2048, 1024, 1024], "ppo_epochs": 2, "ppo_batch_size": 500000, "ppo_minibatch_size": 250000, "ppo_ent_coef": 0.01, "ppo_clip_range": 0.2, "gae_lambda": 0.95, "gae_gamma": 0.99, "policy_lr": 0.0001, "critic_lr": 0.0001, "shm_buffer_size": 8192}}