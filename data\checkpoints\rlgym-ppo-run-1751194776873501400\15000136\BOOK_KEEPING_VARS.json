{"cumulative_timesteps": 15000136, "cumulative_model_updates": 116, "policy_average_reward": -176.24745579836636, "epoch": 29, "ts_since_last_save": 1000008, "reward_running_stats": {"mean": [-23.888364791870117], "var": [4289041.5], "shape": [1], "count": 4500}, "wandb_run_id": "yd7kf81k", "wandb_project": "rlgym-ppo", "wandb_entity": "isa1ah56dev-pinterest", "wandb_group": "unnamed-runs", "wandb_config": {"n_proc": 6, "min_inference_size": 5, "timestep_limit": 1000000000, "exp_buffer_size": 1000000, "ts_per_iteration": 500000, "standardize_returns": true, "standardize_obs": false, "policy_layer_sizes": [2048, 2048, 1024, 1024], "critic_layer_sizes": [2048, 2048, 1024, 1024], "ppo_epochs": 2, "ppo_batch_size": 500000, "ppo_minibatch_size": 250000, "ppo_ent_coef": 0.01, "ppo_clip_range": 0.2, "gae_lambda": 0.95, "gae_gamma": 0.99, "policy_lr": 0.0001, "critic_lr": 0.0001, "shm_buffer_size": 8192}}