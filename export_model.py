"""
Export trained PPO model for RLBot usage.
This script converts the rlgym-ppo checkpoint to a standalone PyTorch model.
"""

import torch
import numpy as np
import json
from pathlib import Path


class CarrotsBotPolicy(torch.nn.Module):
    """Standalone policy model for RLBot usage."""

    def __init__(self, state_dict, layer_sizes=None, input_size=None, output_size=8):
        super().__init__()

        # Default layer sizes if not provided
        if layer_sizes is None:
            layer_sizes = [2048, 2048, 1024, 1024]

        print(f"Using layer sizes: {layer_sizes}")
        print(f"Input size: {input_size}, Output size: {output_size}")

        # Build the network
        layers = []
        prev_size = input_size

        for i, size in enumerate(layer_sizes):
            layers.append(torch.nn.Linear(prev_size, size))
            layers.append(torch.nn.ReLU())
            prev_size = size

        # Final output layer
        layers.append(torch.nn.Linear(prev_size, output_size))

        self.actor = torch.nn.Sequential(*layers)

        # Load the trained weights
        try:
            self.load_state_dict(state_dict, strict=False)
            print("Model weights loaded successfully")
        except Exception as e:
            print(f"Warning: Could not load all weights: {e}")
            # Try to load weights manually
            self._manual_weight_loading(state_dict)

        self.eval()

    def _manual_weight_loading(self, state_dict):
        """Manually load weights if automatic loading fails."""
        print("Attempting manual weight loading...")

        with torch.no_grad():
            for key, value in state_dict.items():
                if ('actor' in key or 'model' in key) and 'weight' in key:
                    try:
                        # Extract layer number
                        layer_num = int(key.split('.')[1])
                        actual_layer_idx = layer_num  # Direct mapping

                        if actual_layer_idx < len(self.actor):
                            if hasattr(self.actor[actual_layer_idx], 'weight'):
                                if self.actor[actual_layer_idx].weight.shape == value.shape:
                                    self.actor[actual_layer_idx].weight.copy_(value)
                                    print(f"Loaded weight for layer {actual_layer_idx}")
                                else:
                                    print(f"Shape mismatch for layer {actual_layer_idx}: {self.actor[actual_layer_idx].weight.shape} vs {value.shape}")
                    except Exception as e:
                        print(f"Error loading weight {key}: {e}")

                elif ('actor' in key or 'model' in key) and 'bias' in key:
                    try:
                        layer_num = int(key.split('.')[1])
                        actual_layer_idx = layer_num

                        if actual_layer_idx < len(self.actor):
                            if hasattr(self.actor[actual_layer_idx], 'bias'):
                                if self.actor[actual_layer_idx].bias.shape == value.shape:
                                    self.actor[actual_layer_idx].bias.copy_(value)
                                    print(f"Loaded bias for layer {actual_layer_idx}")
                    except Exception as e:
                        print(f"Error loading bias {key}: {e}")

    def forward(self, obs):
        """Forward pass through the policy network."""
        if isinstance(obs, np.ndarray):
            obs = torch.FloatTensor(obs)

        # Ensure batch dimension
        if obs.dim() == 1:
            obs = obs.unsqueeze(0)

        return self.actor(obs)

    def get_action(self, obs, deterministic=True):
        """Get action from observation."""
        with torch.no_grad():
            logits = self.forward(obs)
            if deterministic:
                # Use tanh to bound actions to [-1, 1]
                return torch.tanh(logits)
            else:
                # Add some noise for exploration
                noise = torch.randn_like(logits) * 0.1
                return torch.tanh(logits + noise)


def export_latest_model():
    """Export the latest trained model to a standalone .pt file for RLBot."""

    # Find the latest checkpoint
    checkpoint_dir = Path("data/checkpoints")
    if not checkpoint_dir.exists():
        print("No checkpoints directory found!")
        return None

    # Find the run directory (should be only one)
    run_dirs = list(checkpoint_dir.iterdir())
    if not run_dirs:
        print("No run directories found!")
        return None

    run_dir = run_dirs[0]  # Take the first (and likely only) run
    print(f"Found run directory: {run_dir}")

    # Find all checkpoint directories and get the latest one
    checkpoint_dirs = [d for d in run_dir.iterdir() if d.is_dir()]
    if not checkpoint_dirs:
        print("No checkpoint directories found!")
        return None

    # Sort by timestep number (directory name)
    checkpoint_dirs.sort(key=lambda x: int(x.name))
    latest_checkpoint = checkpoint_dirs[-1]

    print(f"Using latest checkpoint: {latest_checkpoint}")

    # Load the policy model
    policy_path = latest_checkpoint / "PPO_POLICY.pt"
    if not policy_path.exists():
        print(f"Policy file not found: {policy_path}")
        return None

    # Load bookkeeping vars to get model configuration
    config = None
    bookkeeping_path = latest_checkpoint / "BOOK_KEEPING_VARS.json"
    if bookkeeping_path.exists():
        with open(bookkeeping_path, 'r') as f:
            config = json.load(f)
        print(f"Model trained for {config['cumulative_timesteps']} timesteps")
        if 'wandb_config' in config and 'policy_layer_sizes' in config['wandb_config']:
            print(f"Policy layers: {config['wandb_config']['policy_layer_sizes']}")

    # Load the policy model
    print(f"Loading policy from: {policy_path}")
    try:
        policy_state_dict = torch.load(policy_path, map_location='cpu')
        print("Policy state dict loaded successfully")
        print(f"State dict keys: {list(policy_state_dict.keys())[:5]}...")  # Show first 5 keys
    except Exception as e:
        print(f"Error loading policy state dict: {e}")
        return None

    # Get layer sizes from config if available
    layer_sizes = [2048, 2048, 1024, 1024]  # Default
    if config and 'wandb_config' in config and 'policy_layer_sizes' in config['wandb_config']:
        layer_sizes = config['wandb_config']['policy_layer_sizes']

    # Determine input and output sizes from state dict
    input_size = None
    for key in policy_state_dict.keys():
        if ('actor' in key or 'model' in key) and 'weight' in key and '0' in key:
            input_size = policy_state_dict[key].shape[1]
            print(f"Found input size from key '{key}': {input_size}")
            break

    if input_size is None:
        print("Could not determine input size from state dict")
        print("Available keys:", list(policy_state_dict.keys()))
        return None

    # Determine output size from state dict
    output_size = 8  # Default for Rocket League
    layer_nums = []

    # Find all layer numbers
    for key in policy_state_dict.keys():
        if ('actor' in key or 'model' in key) and 'weight' in key:
            try:
                num = int(key.split('.')[1])
                layer_nums.append(num)
            except:
                continue

    if layer_nums:
        max_layer = max(layer_nums)
        # Try both naming conventions
        final_key_options = [f"model.{max_layer}.weight", f"actor.{max_layer}.weight"]
        for final_key in final_key_options:
            if final_key in policy_state_dict:
                output_size = policy_state_dict[final_key].shape[0]
                print(f"Found output size from key '{final_key}': {output_size}")
                break

    print(f"Network architecture: {input_size} -> {layer_sizes} -> {output_size}")

    # Create the policy model
    try:

        print("Creating CarrotsBotPolicy...")
        policy_model = CarrotsBotPolicy(
            state_dict=policy_state_dict,
            layer_sizes=layer_sizes,
            input_size=input_size,
            output_size=output_size
        )

        # Export the model using state_dict to avoid pickling issues
        export_path = "carrots_bot_policy.pt"
        export_data = {
            'model_state_dict': policy_model.state_dict(),
            'layer_sizes': layer_sizes,
            'input_size': input_size,
            'output_size': output_size,
            'model_class': 'CarrotsBotPolicy'
        }
        torch.save(export_data, export_path)
        print(f"Model exported successfully to: {export_path}")

        # Test the model with dummy input
        try:
            dummy_input = torch.randn(1, input_size)
            output = policy_model.get_action(dummy_input)
            print(f"Test successful!")
            print(f"Input shape: {dummy_input.shape}")
            print(f"Output shape: {output.shape}")
            print(f"Output range: [{output.min():.3f}, {output.max():.3f}]")
        except Exception as e:
            print(f"Test failed: {e}")

        return export_path

    except Exception as e:
        print(f"Error creating policy model: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    result = export_latest_model()
    if result:
        print(f"\n✅ SUCCESS: Model exported to {result}")
        print("You can now use this model with RLBot!")
    else:
        print("\n❌ FAILED: Could not export model")
        print("Please check your checkpoint files and try again.")
