"""
Export trained PPO model for RLBot usage.
This script converts the rlgym-ppo checkpoint to a standalone PyTorch model.
"""

import torch
import numpy as np
import json
from pathlib import Path


def export_latest_model():
    """Export the latest trained model to a standalone .pt file for RLBot."""
    
    # Find the latest checkpoint
    checkpoint_dir = Path("data/checkpoints")
    if not checkpoint_dir.exists():
        print("No checkpoints directory found!")
        return
    
    # Find the run directory (should be only one)
    run_dirs = list(checkpoint_dir.iterdir())
    if not run_dirs:
        print("No run directories found!")
        return
    
    run_dir = run_dirs[0]  # Take the first (and likely only) run
    print(f"Found run directory: {run_dir}")
    
    # Find all checkpoint directories and get the latest one
    checkpoint_dirs = [d for d in run_dir.iterdir() if d.is_dir()]
    if not checkpoint_dirs:
        print("No checkpoint directories found!")
        return
    
    # Sort by timestep number (directory name)
    checkpoint_dirs.sort(key=lambda x: int(x.name))
    latest_checkpoint = checkpoint_dirs[-1]
    
    print(f"Using latest checkpoint: {latest_checkpoint}")
    
    # Load the policy model
    policy_path = latest_checkpoint / "PPO_POLICY.pt"
    if not policy_path.exists():
        print(f"Policy file not found: {policy_path}")
        return
    
    # Load bookkeeping vars to get model configuration
    bookkeeping_path = latest_checkpoint / "BOOK_KEEPING_VARS.json"
    if bookkeeping_path.exists():
        with open(bookkeeping_path, 'r') as f:
            config = json.load(f)
        print(f"Model trained for {config['cumulative_timesteps']} timesteps")
        print(f"Policy layers: {config['wandb_config']['policy_layer_sizes']}")
    
    # Load the policy model
    print(f"Loading policy from: {policy_path}")
    policy_state_dict = torch.load(policy_path, map_location='cpu')
    
    # Create a simple wrapper class for the policy
    class CarrotsBotPolicy(torch.nn.Module):
        """Standalone policy model for RLBot usage."""
        
        def __init__(self, state_dict, layer_sizes=None):
            super().__init__()
            
            # Default layer sizes if not provided
            if layer_sizes is None:
                layer_sizes = [2048, 2048, 1024, 1024]
            
            # Reconstruct the policy network architecture
            # This matches the rlgym-ppo policy structure
            layers = []
            
            # Input layer - we need to determine input size from state dict
            input_size = None
            for key in state_dict.keys():
                if 'actor.0.weight' in key:
                    input_size = state_dict[key].shape[1]
                    break
            
            if input_size is None:
                print("Could not determine input size from state dict")
                return
            
            print(f"Input size: {input_size}")
            
            # Build the network
            prev_size = input_size
            for i, size in enumerate(layer_sizes):
                layers.append(torch.nn.Linear(prev_size, size))
                layers.append(torch.nn.ReLU())
                prev_size = size
            
            # Output layer - determine output size from state dict
            output_size = None
            for key in state_dict.keys():
                if 'actor.' in key and 'weight' in key:
                    if key.split('.')[-2].isdigit():
                        layer_num = int(key.split('.')[-2])
                        if layer_num == len(layer_sizes) * 2:  # Final layer
                            output_size = state_dict[key].shape[0]
                            break
            
            if output_size is None:
                # Default to 8 actions for Rocket League
                output_size = 8
            
            print(f"Output size: {output_size}")
            layers.append(torch.nn.Linear(prev_size, output_size))
            
            self.actor = torch.nn.Sequential(*layers)
            
            # Load the trained weights
            self.load_state_dict(state_dict, strict=False)
            self.eval()
        
        def forward(self, obs):
            """Forward pass through the policy network."""
            if isinstance(obs, np.ndarray):
                obs = torch.FloatTensor(obs)
            
            # Ensure batch dimension
            if obs.dim() == 1:
                obs = obs.unsqueeze(0)
            
            return self.actor(obs)
        
        def get_action(self, obs, deterministic=True):
            """Get action from observation."""
            with torch.no_grad():
                logits = self.forward(obs)
                if deterministic:
                    # Use the mean of the distribution (deterministic policy)
                    return torch.tanh(logits)
                else:
                    # Sample from the distribution
                    return torch.tanh(logits + torch.randn_like(logits) * 0.1)
    
    # Create the policy model
    try:
        # Try to get layer sizes from config
        layer_sizes = None
        if 'config' in locals() and 'wandb_config' in config:
            layer_sizes = config['wandb_config'].get('policy_layer_sizes')
        
        policy_model = CarrotsBotPolicy(policy_state_dict, layer_sizes)
        
        # Export the model
        export_path = "carrots_bot_policy.pt"
        torch.save(policy_model, export_path)
        print(f"Model exported successfully to: {export_path}")
        
        # Test the model with dummy input
        dummy_input = torch.randn(1, policy_model.actor[0].in_features)
        output = policy_model.get_action(dummy_input)
        print(f"Test output shape: {output.shape}")
        print(f"Test output range: [{output.min():.3f}, {output.max():.3f}]")
        
        return export_path
        
    except Exception as e:
        print(f"Error creating policy model: {e}")
        return None


if __name__ == "__main__":
    export_latest_model()
