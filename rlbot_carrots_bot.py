"""
RLBot integration for the Carrots-trained PPO bot.
This bot uses the exported PyTorch model from rlgym-ppo training.
"""

import torch
import numpy as np
from pathlib import Path

# RLBot imports
from rlbot.agents.base_agent import BaseAgent, SimpleControllerState
from rlbot.utils.structures.game_data_struct import GameTickPacket
from rlbot.utils.game_state_util import GameState


class CarrotsBot(BaseAgent):
    """
    RLBot implementation of the Carrots-trained PPO bot.
    Uses the sophisticated reward system trained model for gameplay.
    """
    
    def __init__(self, name, team, index):
        super().__init__(name, team, index)
        self.model = None
        self.obs_builder = None
        self.action_parser = None
        self.model_loaded = False
        
    def initialize_agent(self):
        """Initialize the bot and load the trained model."""
        print(f"Initializing CarrotsBot {self.name}")
        
        # Load the exported model
        model_path = Path(__file__).parent / "carrots_bot_policy.pt"
        
        if not model_path.exists():
            print(f"Model file not found: {model_path}")
            print("Please run export_model.py first to export the trained model!")
            return
        
        try:
            self.model = torch.load(model_path, map_location='cpu')
            self.model.eval()
            self.model_loaded = True
            print(f"Successfully loaded model from {model_path}")
        except Exception as e:
            print(f"Error loading model: {e}")
            return
        
        # Initialize observation builder (simplified version of the training obs)
        self.obs_builder = RLBotObsBuilder()
        
        # Initialize action parser
        self.action_parser = RLBotActionParser()
        
        print("CarrotsBot initialized successfully!")
    
    def get_output(self, packet: GameTickPacket) -> SimpleControllerState:
        """Main bot logic - get action from the trained model."""
        
        if not self.model_loaded:
            # Return default controls if model not loaded
            return SimpleControllerState()
        
        try:
            # Build observation from game packet
            obs = self.obs_builder.build_obs(packet, self.index)
            
            # Get action from model
            with torch.no_grad():
                action_tensor = self.model.get_action(obs, deterministic=True)
                action = action_tensor.cpu().numpy().flatten()
            
            # Convert action to RLBot controls
            controls = self.action_parser.parse_actions(action)
            
            return controls
            
        except Exception as e:
            print(f"Error in get_output: {e}")
            return SimpleControllerState()


class RLBotObsBuilder:
    """
    Simplified observation builder for RLBot.
    Creates observations compatible with the trained model.
    """
    
    def __init__(self):
        # This should match the observation space used during training
        self.obs_size = 107  # Adjust based on your training obs size
    
    def build_obs(self, packet: GameTickPacket, player_index: int):
        """Build observation vector from RLBot game packet."""
        
        if packet.num_cars <= player_index:
            return np.zeros(self.obs_size)
        
        player = packet.game_cars[player_index]
        ball = packet.game_ball
        
        obs = []
        
        # Ball information
        obs.extend([
            ball.physics.location.x / 4096,  # Normalize positions
            ball.physics.location.y / 5120,
            ball.physics.location.z / 2044,
            ball.physics.velocity.x / 2300,  # Normalize velocities
            ball.physics.velocity.y / 2300,
            ball.physics.velocity.z / 2300,
            ball.physics.angular_velocity.x / 5.5,
            ball.physics.angular_velocity.y / 5.5,
            ball.physics.angular_velocity.z / 5.5,
        ])
        
        # Player information
        obs.extend([
            player.physics.location.x / 4096,
            player.physics.location.y / 5120,
            player.physics.location.z / 2044,
            player.physics.velocity.x / 2300,
            player.physics.velocity.y / 2300,
            player.physics.velocity.z / 2300,
            player.physics.angular_velocity.x / 5.5,
            player.physics.angular_velocity.y / 5.5,
            player.physics.angular_velocity.z / 5.5,
        ])
        
        # Player rotation (as rotation matrix elements)
        pitch = player.physics.rotation.pitch
        yaw = player.physics.rotation.yaw
        roll = player.physics.rotation.roll
        
        # Convert to rotation matrix elements (simplified)
        obs.extend([
            np.cos(yaw) * np.cos(pitch),  # Forward X
            np.sin(yaw) * np.cos(pitch),  # Forward Y
            -np.sin(pitch),               # Forward Z
            -np.sin(yaw),                 # Right X
            np.cos(yaw),                  # Right Y
            0,                            # Right Z
            np.cos(yaw) * np.sin(pitch),  # Up X
            np.sin(yaw) * np.sin(pitch),  # Up Y
            np.cos(pitch),                # Up Z
        ])
        
        # Player state
        obs.extend([
            player.boost / 100.0,  # Normalize boost
            1.0 if player.has_wheel_contact else 0.0,
            1.0 if player.is_super_sonic else 0.0,
            1.0 if player.jumped else 0.0,
            1.0 if player.double_jumped else 0.0,
        ])
        
        # Relative ball position
        rel_ball_x = (ball.physics.location.x - player.physics.location.x) / 4096
        rel_ball_y = (ball.physics.location.y - player.physics.location.y) / 5120
        rel_ball_z = (ball.physics.location.z - player.physics.location.z) / 2044
        
        obs.extend([rel_ball_x, rel_ball_y, rel_ball_z])
        
        # Add opponent information (simplified - just closest opponent)
        closest_opponent = None
        min_dist = float('inf')
        
        for i in range(packet.num_cars):
            if i != player_index and packet.game_cars[i].team != player.team:
                opponent = packet.game_cars[i]
                dist = np.sqrt(
                    (opponent.physics.location.x - player.physics.location.x) ** 2 +
                    (opponent.physics.location.y - player.physics.location.y) ** 2
                )
                if dist < min_dist:
                    min_dist = dist
                    closest_opponent = opponent
        
        if closest_opponent:
            obs.extend([
                closest_opponent.physics.location.x / 4096,
                closest_opponent.physics.location.y / 5120,
                closest_opponent.physics.location.z / 2044,
                closest_opponent.physics.velocity.x / 2300,
                closest_opponent.physics.velocity.y / 2300,
                closest_opponent.physics.velocity.z / 2300,
            ])
        else:
            obs.extend([0, 0, 0, 0, 0, 0])
        
        # Boost pad information (simplified - just a few key pads)
        boost_pads = [0] * 34  # 34 boost pads total
        for i, pad in enumerate(packet.game_boosts):
            if i < 34:
                boost_pads[i] = 1.0 if pad.is_active else 0.0
        
        obs.extend(boost_pads[:10])  # Just use first 10 for simplicity
        
        # Pad to expected size
        while len(obs) < self.obs_size:
            obs.append(0.0)
        
        # Truncate if too long
        obs = obs[:self.obs_size]
        
        return torch.FloatTensor(obs)


class RLBotActionParser:
    """
    Action parser to convert model output to RLBot controls.
    """
    
    def parse_actions(self, actions):
        """Convert action array to RLBot SimpleControllerState."""
        controls = SimpleControllerState()
        
        # Ensure we have enough actions
        if len(actions) < 8:
            actions = np.pad(actions, (0, 8 - len(actions)), 'constant')
        
        # Map actions to controls (standard continuous action mapping)
        controls.throttle = float(np.clip(actions[0], -1, 1))
        controls.steer = float(np.clip(actions[1], -1, 1))
        controls.pitch = float(np.clip(actions[2], -1, 1))
        controls.yaw = float(np.clip(actions[3], -1, 1))
        controls.roll = float(np.clip(actions[4], -1, 1))
        controls.jump = bool(actions[5] > 0)
        controls.boost = bool(actions[6] > 0)
        controls.handbrake = bool(actions[7] > 0)
        
        return controls
