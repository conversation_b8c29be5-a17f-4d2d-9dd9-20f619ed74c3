"""
Setup script for RLBot integration.
This script exports the trained model and sets up everything needed for RLBot.
"""

import os
import sys
import subprocess
from pathlib import Path


def check_requirements():
    """Check if required packages are installed."""
    required_packages = ['torch', 'numpy', 'rlbot']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"Missing required packages: {missing_packages}")
        print("Please install them with:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True


def export_model():
    """Export the trained model for RLBot usage."""
    print("Exporting trained model...")
    
    try:
        # Import and run the export function
        from export_model import export_latest_model
        model_path = export_latest_model()
        
        if model_path:
            print(f"Model exported successfully: {model_path}")
            return True
        else:
            print("Failed to export model")
            return False
            
    except Exception as e:
        print(f"Error exporting model: {e}")
        return False


def setup_rlbot():
    """Set up RLBot configuration."""
    print("Setting up RLBot configuration...")
    
    # Check if all required files exist
    required_files = [
        'rlbot_carrots_bot.py',
        'carrots_bot.cfg',
        'appearance.cfg',
        'carrots_match.cfg',
        'carrots_bot_policy.pt'
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"Missing required files: {missing_files}")
        return False
    
    print("All required files found!")
    return True


def run_rlbot():
    """Launch RLBot with the CarrotsBot."""
    print("Launching RLBot...")
    
    try:
        # Try to run RLBot with the match configuration
        subprocess.run(['rlbot', 'carrots_match.cfg'], check=True)
    except subprocess.CalledProcessError as e:
        print(f"Error running RLBot: {e}")
        print("You can also run manually with: rlbot carrots_match.cfg")
    except FileNotFoundError:
        print("RLBot not found in PATH. Please install RLBot:")
        print("pip install rlbot")
        print("Then run: rlbot carrots_match.cfg")


def main():
    """Main setup function."""
    print("=== CarrotsBot RLBot Setup ===")
    
    # Check requirements
    if not check_requirements():
        return
    
    # Export model
    if not export_model():
        print("Failed to export model. Please check your training checkpoints.")
        return
    
    # Setup RLBot
    if not setup_rlbot():
        print("Failed to setup RLBot configuration.")
        return
    
    print("\n=== Setup Complete! ===")
    print("Your CarrotsBot is ready for RLBot!")
    print("\nTo run the bot:")
    print("1. Make sure Rocket League is running")
    print("2. Run: rlbot carrots_match.cfg")
    print("   OR")
    print("3. Run: python setup_rlbot.py --run")
    print("\nThe bot will use your trained 'Carrots' reward system!")
    
    # Check if user wants to run immediately
    if '--run' in sys.argv:
        run_rlbot()


if __name__ == "__main__":
    main()
