2025-06-28 14:35:02,888 INFO    MainThread:30648 [wandb_setup.py:_flush():81] Current SDK version is 0.20.1
2025-06-28 14:35:02,888 INFO    MainThread:30648 [wandb_setup.py:_flush():81] Configure stats pid to 30648
2025-06-28 14:35:02,888 INFO    MainThread:30648 [wandb_setup.py:_flush():81] Loading settings from C:\Users\<USER>\.config\wandb\settings
2025-06-28 14:35:02,888 INFO    MainThread:30648 [wandb_setup.py:_flush():81] Loading settings from C:\Users\<USER>\Downloads\rlgym-ppo-main\wandb\settings
2025-06-28 14:35:02,889 INFO    MainThread:30648 [wandb_setup.py:_flush():81] Loading settings from environment variables
2025-06-28 14:35:02,889 INFO    MainThread:30648 [wandb_init.py:setup_run_log_directory():703] Logging user logs to C:\Users\<USER>\Downloads\rlgym-ppo-main\wandb\run-20250628_143502-nij3f53e\logs\debug.log
2025-06-28 14:35:02,889 INFO    MainThread:30648 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to C:\Users\<USER>\Downloads\rlgym-ppo-main\wandb\run-20250628_143502-nij3f53e\logs\debug-internal.log
2025-06-28 14:35:02,889 INFO    MainThread:30648 [wandb_init.py:init():831] calling init triggers
2025-06-28 14:35:02,889 INFO    MainThread:30648 [wandb_init.py:init():836] wandb.init called with sweep_config: {}
config: {'n_proc': 6, 'min_inference_size': 5, 'timestep_limit': 1000000000, 'exp_buffer_size': 300000, 'ts_per_iteration': 100000, 'standardize_returns': True, 'standardize_obs': False, 'policy_layer_sizes': [2048, 2048, 1024, 1024], 'critic_layer_sizes': [2048, 2048, 1024, 1024], 'ppo_epochs': 2, 'ppo_batch_size': 100000, 'ppo_minibatch_size': 50000, 'ppo_ent_coef': 0.01, 'ppo_clip_range': 0.2, 'gae_lambda': 0.95, 'gae_gamma': 0.99, 'policy_lr': 0.0001, 'critic_lr': 0.0001, 'shm_buffer_size': 8192, '_wandb': {}}
2025-06-28 14:35:02,889 INFO    MainThread:30648 [wandb_init.py:init():872] starting backend
2025-06-28 14:35:03,118 INFO    MainThread:30648 [wandb_init.py:init():875] sending inform_init request
2025-06-28 14:35:03,160 INFO    MainThread:30648 [wandb_init.py:init():883] backend started and connected
2025-06-28 14:35:03,163 INFO    MainThread:30648 [wandb_init.py:init():956] updated telemetry
2025-06-28 14:35:03,165 INFO    MainThread:30648 [wandb_init.py:init():980] communicating run to backend with 90.0 second timeout
2025-06-28 14:35:03,829 INFO    MainThread:30648 [wandb_init.py:init():1032] starting run threads in backend
2025-06-28 14:35:04,081 INFO    MainThread:30648 [wandb_run.py:_console_start():2453] atexit reg
2025-06-28 14:35:04,082 INFO    MainThread:30648 [wandb_run.py:_redirect():2301] redirect: wrap_raw
2025-06-28 14:35:04,082 INFO    MainThread:30648 [wandb_run.py:_redirect():2370] Wrapping output streams.
2025-06-28 14:35:04,082 INFO    MainThread:30648 [wandb_run.py:_redirect():2393] Redirects installed.
2025-06-28 14:35:04,085 INFO    MainThread:30648 [wandb_init.py:init():1078] run started, returning control to user process
