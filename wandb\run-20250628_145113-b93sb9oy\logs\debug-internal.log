{"time":"2025-06-28T14:51:14.0278939-05:00","level":"INFO","msg":"stream: starting","core version":"0.20.1","symlink path":"C:\\Users\\<USER>\\Downloads\\rlgym-ppo-main\\wandb\\run-20250628_145113-b93sb9oy\\logs\\debug-core.log"}
{"time":"2025-06-28T14:51:14.350662-05:00","level":"INFO","msg":"stream: created new stream","id":"b93sb9oy"}
{"time":"2025-06-28T14:51:14.3511779-05:00","level":"INFO","msg":"stream: started","id":"b93sb9oy"}
{"time":"2025-06-28T14:51:14.3511779-05:00","level":"INFO","msg":"handler: started","stream_id":"b93sb9oy"}
{"time":"2025-06-28T14:51:14.3511779-05:00","level":"INFO","msg":"sender: started","stream_id":"b93sb9oy"}
{"time":"2025-06-28T14:51:14.3511779-05:00","level":"INFO","msg":"writer: Do: started","stream_id":"b93sb9oy"}
{"time":"2025-06-28T14:51:14.4772612-05:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-06-28T14:56:08.3003831-05:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/files/isa1ah56dev-pinterest/rlgym-ppo/b93sb9oy/file_stream\": http2: client conn could not be established"}
{"time":"2025-06-28T14:56:14.9270539-05:00","level":"INFO","msg":"api: retrying error","error":"Post \"https://api.wandb.ai/graphql\": context deadline exceeded (Client.Timeout exceeded while awaiting headers)"}
